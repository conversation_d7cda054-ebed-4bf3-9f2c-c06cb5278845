import { NextRequest, NextResponse } from 'next/server';

// 使用Edge Runtime提高性能
export const runtime = 'edge';

/**
 * 图片代理API - 这是解决跨域下载问题的关键
 * 工作原理：接收图片URL → 服务器获取图片 → 返回给浏览器下载
 */
export async function GET(request: NextRequest) {
  try {
    // 步骤1：从URL参数中获取要下载的图片地址
    const url = request.nextUrl.searchParams.get('url');

    console.log('接收到的图片URL:', url?.substring(0, 100) + '...');

    // 步骤2：检查是否提供了URL参数
    if (!url) {
      return new NextResponse('缺少URL参数', { status: 400 });
    }

    // 步骤3：解码和验证URL格式
    let imageUrl: string;
    try {
      imageUrl = decodeURIComponent(url);  // 解码URL（因为前端用了encodeURIComponent）
      new URL(imageUrl);                   // 验证URL格式是否正确
    } catch (error) {
      console.error('无效的图片URL:', error);
      return new NextResponse('无效的图片URL', { status: 400 });
    }

    console.log('开始获取图片:', imageUrl.substring(0, 100) + '...');

    // 步骤4：从原始地址获取图片数据
    const imageResponse = await fetch(imageUrl, {
      headers: {
        // 模拟浏览器请求，避免被服务器拒绝
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        // 指定接受的图片格式
        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
        // 不使用缓存，确保获取最新图片
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      cache: 'no-store'  // 不使用缓存
    });

    // 步骤5：检查图片获取是否成功
    if (!imageResponse.ok) {
      console.error('获取图片失败:', imageResponse.status, imageResponse.statusText);
      return new NextResponse(
        `获取图片失败: ${imageResponse.status} ${imageResponse.statusText}`,
        { status: imageResponse.status }
      );
    }

    // 步骤6：获取图片的二进制数据
    const imageBuffer = await imageResponse.arrayBuffer();  // 获取图片的二进制数据
    const contentType = imageResponse.headers.get('content-type') || 'image/jpeg';  // 获取图片类型

    console.log('图片大小:', imageBuffer.byteLength, '字节, 类型:', contentType);
    // 步骤7：设置下载响应头（重要！）
    const headers = new Headers();
    headers.set('Content-Type', contentType);  // 设置内容类型

    // 这一行很重要：告诉浏览器这是要下载的文件，而不是要显示的图片
    headers.set('Content-Disposition', 'attachment; filename="kontext-dev.com.png"');

    headers.set('Content-Length', imageBuffer.byteLength.toString());  // 设置文件大小

    // 设置缓存控制，确保每次都是最新的
    headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    headers.set('Pragma', 'no-cache');
    headers.set('Expires', '0');
    // 步骤8：返回图片数据给浏览器
    return new NextResponse(imageBuffer, {
      status: 200,
      headers
    });

  } catch (error) {
    // 错误处理
    console.error('代理图片时出错:', error);
    return new NextResponse(
      `代理图片时出错: ${(error as Error).message}`,
      { status: 500 }
    );
  }
}